<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shadow DOM事件处理测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-text {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            cursor: text;
            user-select: text;
        }
        
        .instructions {
            background: #fff3e0;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #ff9800;
        }
        
        .debug-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
        }
        
        .debug-panel h3 {
            margin-top: 0;
            color: #333;
        }
        
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
        }
        
        .clear-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        .clear-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🔍 Shadow DOM事件处理测试页面</h1>
    
    <div class="instructions">
        <h3>测试说明：</h3>
        <ol>
            <li>选中下面的测试文本，会出现划词工具栏</li>
            <li>点击工具栏上的按钮，观察事件传播</li>
            <li>特别测试"继续问"按钮，确认弹窗不会意外关闭</li>
            <li>查看右侧调试面板的事件日志</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>测试文本区域</h2>
        <div class="test-text">
            这是一段用于测试Shadow DOM事件处理的文本。请选中这段文字，然后观察划词工具栏的行为。
            特别要测试AIProcessModal中的"继续问"按钮是否会导致弹窗意外关闭。
        </div>
        
        <div class="test-text">
            另一段测试文本：在Chrome浏览器插件开发中，Shadow DOM提供了完美的样式和DOM隔离解决方案。
            通过正确的事件处理机制，可以确保插件组件与宿主页面完全隔离。
        </div>
        
        <div class="test-text">
            第三段测试文本：事件传播路径分析是调试Shadow DOM问题的关键。
            使用composedPath()方法可以获取完整的事件传播路径，包括跨越Shadow DOM边界的路径。
        </div>
    </div>

    <div class="test-section">
        <h2>宿主页面事件监听器</h2>
        <p>这个页面模拟了宿主页面的事件监听器，用于测试事件冲突。</p>
        <button id="host-button">宿主页面按钮</button>
    </div>

    <!-- 调试面板 -->
    <div class="debug-panel">
        <h3>🐛 事件调试日志</h3>
        <div class="debug-log" id="debug-log"></div>
        <button class="clear-btn" onclick="clearDebugLog()">清空日志</button>
    </div>

    <script>
        // 模拟宿主页面的事件监听器
        let debugLog = document.getElementById('debug-log');
        
        function addDebugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        function clearDebugLog() {
            debugLog.innerHTML = '';
        }
        
        // 监听宿主页面的点击事件
        document.addEventListener('click', (e) => {
            const target = e.target;
            const eventPath = e.composedPath();
            
            addDebugLog(`🖱️ 宿主页面捕获点击: ${target.tagName} (${target.className || target.id || 'no-class'})`);
            addDebugLog(`📍 事件路径长度: ${eventPath.length}`);
            
            // 检查是否是插件相关的点击
            const isPluginClick = eventPath.some(node => {
                if (node instanceof Element) {
                    return node.id === 'web-assistant-main-container' ||
                           node.className?.includes('web-assistant') ||
                           node.className?.includes('selection') ||
                           node.className?.includes('modal');
                }
                return false;
            });
            
            if (isPluginClick) {
                addDebugLog(`🎯 检测到插件内部点击`);
            } else {
                addDebugLog(`🌐 宿主页面点击`);
            }
        });
        
        // 监听选择变化
        document.addEventListener('selectionchange', () => {
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
                addDebugLog(`📝 文本选择: "${selection.toString().slice(0, 30)}..."`);
            }
        });
        
        // 宿主页面按钮点击
        document.getElementById('host-button').addEventListener('click', (e) => {
            addDebugLog(`🔘 宿主页面按钮被点击`);
            alert('宿主页面按钮点击事件触发');
        });
        
        // 监听键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                addDebugLog(`⌨️ ESC键按下`);
            }
        });
        
        // 页面加载完成
        window.addEventListener('load', () => {
            addDebugLog(`🚀 测试页面加载完成`);
            addDebugLog(`💡 请选中文本来测试Shadow DOM事件处理`);
        });
    </script>
</body>
</html>
