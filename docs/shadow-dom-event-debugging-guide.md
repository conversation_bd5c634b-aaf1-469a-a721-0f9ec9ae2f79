# Shadow DOM事件处理机制调试指南

## 问题分析

### 1. 事件传播路径分析

在你的Chrome插件中，当点击划词工具栏（SelectionBar）上的按钮时，事件传播路径如下：

```
用户点击 → Shadow DOM内的按钮元素 → Shadow Root → 主容器 → document → window
```

**关键点：**
- 事件首先在Shadow DOM内部传播
- 然后通过Shadow DOM边界传播到宿主页面
- `document.addEventListener('click', handleDocumentClick)` 会捕获所有点击事件，包括Shadow DOM内的事件

### 2. 事件阻止机制详解

你在代码中使用的三种事件阻止方法各有不同作用：

```typescript
const handleContinueAsk = (e: React.MouseEvent) => {
  e.stopPropagation();                    // 阻止React合成事件冒泡
  e.preventDefault();                     // 阻止默认行为
  e.nativeEvent.stopImmediatePropagation(); // 阻止原生事件立即传播
  setShowContinueInput(true);
};
```

**详细说明：**
- `e.stopPropagation()`: 阻止React合成事件在虚拟DOM树中继续冒泡
- `e.preventDefault()`: 阻止浏览器的默认行为（如表单提交、链接跳转等）
- `e.nativeEvent.stopImmediatePropagation()`: 阻止原生DOM事件立即传播，防止同一元素上的其他事件监听器被触发

### 3. AIProcessModal Bug分析

**问题根源：**
AIProcessModal的`modalOverlay`覆盖了整个视口，当点击"继续问"按钮时：

1. 点击事件在按钮上触发
2. 事件冒泡到`modalOverlay`
3. 事件继续冒泡到Shadow DOM外部
4. `document.addEventListener('click', handleDocumentClick)`捕获到事件
5. `handleDocumentClick`检查事件路径，发现不在预期的组件内部
6. 触发`hideSelectionBar()`，导致整个工具栏（包括Modal）关闭

## 解决方案

### 1. 修复AIProcessModal事件处理

```typescript
// 在AIProcessModal/index.tsx中修改handleContinueAsk
const handleContinueAsk = (e: React.MouseEvent) => {
  // 完全阻止事件传播
  e.stopPropagation();
  e.preventDefault();
  e.nativeEvent.stopImmediatePropagation();
  
  console.log('Continue ask clicked, event stopped'); // 调试日志
  setShowContinueInput(true);
};

// 在modalOverlay上也添加事件阻止
return (
  <div 
    className={styles.modalOverlay}
    onClick={(e) => {
      // 只有点击overlay背景时才关闭，点击modal内容时不关闭
      if (e.target === e.currentTarget) {
        handleClose();
      }
      e.stopPropagation(); // 阻止事件继续传播到document
    }}
  >
    <div
      ref={modalRef}
      className={styles.modal}
      onClick={(e) => {
        // 阻止modal内部点击事件冒泡到overlay
        e.stopPropagation();
      }}
    >
      {/* modal内容 */}
    </div>
  </div>
);
```

### 2. 改进webAssistantManager的事件检测

```typescript
private handleDocumentClick = (event: MouseEvent): void => {
  if (this.isSelectionBarVisible && this.selectionBarContainer) {
    const target = event.target as Node;
    const eventPath = event.composedPath();

    // 增强的组件检测逻辑
    const isInsideMainContainer = this.mainContainer && eventPath.includes(this.mainContainer);
    const isInsideShadowDOM = this.shadowRoot && eventPath.some(node =>
      this.shadowRoot!.contains(node as Node)
    );
    const isInsideSelectionBar = this.selectionBarContainer && eventPath.some(node =>
      this.selectionBarContainer!.contains(node as Node)
    );
    
    // 新增：检查是否是AIProcessModal相关的元素
    const isInsideModal = eventPath.some(node => {
      if (node instanceof Element) {
        return node.closest('.modalOverlay') || 
               node.closest('.modal') ||
               node.classList.contains('modalOverlay') ||
               node.classList.contains('modal');
      }
      return false;
    });

    console.log('Document click debug:', {
      target: target,
      eventPathLength: eventPath.length,
      isInsideShadowDOM,
      isInsideMainContainer,
      isInsideSelectionBar,
      isInsideModal, // 新增调试信息
      eventPath: eventPath.map(node => ({
        nodeName: (node as Element).nodeName,
        className: (node as Element).className,
        id: (node as Element).id
      }))
    });

    // 如果点击在我们的组件内部（包括Modal），不隐藏工具栏
    if (isInsideShadowDOM || isInsideMainContainer || isInsideSelectionBar || isInsideModal) {
      console.log('Click inside component area, not hiding');
      return;
    }

    // 检查是否还有选中的文本
    const selection = window.getSelection();
    if (!selection || selection.toString().trim() === '') {
      console.log('Hiding selection bar due to outside click');
      this.hideSelectionBar();
    }
  }
};
```

### 3. 添加调试工具

创建一个调试工具来可视化事件传播路径：

```typescript
// 在webAssistantManager.ts中添加调试方法
private debugEventPath(event: MouseEvent): void {
  const eventPath = event.composedPath();
  console.group('🔍 Event Path Analysis');
  
  eventPath.forEach((node, index) => {
    if (node instanceof Element) {
      console.log(`${index}: ${node.nodeName}`, {
        className: node.className,
        id: node.id,
        isShadowRoot: node === this.shadowRoot,
        isMainContainer: node === this.mainContainer,
        isSelectionBar: node === this.selectionBarContainer
      });
    } else if (node instanceof ShadowRoot) {
      console.log(`${index}: ShadowRoot`, {
        mode: node.mode,
        host: node.host
      });
    } else {
      console.log(`${index}: ${node.constructor.name}`);
    }
  });
  
  console.groupEnd();
}

// 在handleDocumentClick中调用
private handleDocumentClick = (event: MouseEvent): void => {
  // 开发环境下启用详细调试
  if (process.env.NODE_ENV === 'development') {
    this.debugEventPath(event);
  }
  
  // ... 其余逻辑
};
```

## 测试验证

### 1. 事件传播测试

```typescript
// 在AIProcessModal中添加测试代码
useEffect(() => {
  const testEventPropagation = (e: MouseEvent) => {
    console.log('🎯 Modal captured click:', {
      target: e.target,
      currentTarget: e.currentTarget,
      eventPhase: e.eventPhase,
      bubbles: e.bubbles,
      composed: e.composed
    });
  };

  if (modalRef.current) {
    modalRef.current.addEventListener('click', testEventPropagation, true); // 捕获阶段
    modalRef.current.addEventListener('click', testEventPropagation, false); // 冒泡阶段
  }

  return () => {
    if (modalRef.current) {
      modalRef.current.removeEventListener('click', testEventPropagation, true);
      modalRef.current.removeEventListener('click', testEventPropagation, false);
    }
  };
}, []);
```

### 2. Shadow DOM边界测试

```typescript
// 测试事件是否正确跨越Shadow DOM边界
const testShadowBoundary = () => {
  if (this.shadowRoot) {
    this.shadowRoot.addEventListener('click', (e) => {
      console.log('🌟 Shadow DOM click:', {
        target: e.target,
        composed: e.composed,
        composedPath: e.composedPath().length
      });
    });
  }
};
```

## 最佳实践总结

### 1. 事件处理原则
- 在Modal的overlay上正确处理点击事件
- 在所有交互元素上添加适当的事件阻止
- 使用`composedPath()`检查Shadow DOM内的事件路径

### 2. 调试策略
- 使用详细的console.log记录事件传播
- 在开发环境中启用事件路径可视化
- 测试不同的点击场景

### 3. 性能考虑
- 避免在每次点击时都进行复杂的DOM查询
- 使用事件委托减少事件监听器数量
- 在组件卸载时正确清理事件监听器
